import { Injectable, NotFoundException } from '@nestjs/common';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import { ProfileRepository, UsersRepository } from 'src/common/repositories';
import { OrdersRepository } from 'src/common/repositories';

@Injectable()
export class ProfileService {
  constructor(
    private readonly profileRepository: ProfileRepository,
    private readonly cloudinaryService: CloudinaryService,
    private readonly usersRepository: UsersRepository,
    private readonly ordersRepository: OrdersRepository,
  ) {}

  async updateUserProfile(
    userId: string,
    updateUserProfileDto: UpdateUserProfileDto,
    file?: Express.Multer.File,
  ) {
    const { gender, nickname, fullname, phone, dateOfBirth } =
      updateUserProfileDto;

    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user) throw new NotFoundException('User not found.');

      if (fullname || phone) {
        await this.usersRepository.update(userId, {
          ...(fullname && { fullname }),
          ...(phone && { phone }),
        });
      }

      const updateData: any = {
        ...(nickname && { nickname }),
        ...(gender && { gender }),
      };

      if (dateOfBirth && dateOfBirth.trim() !== '') {
        updateData.dateOfBirth = new Date(dateOfBirth);
      }

      if (file) {
        const image = await this.cloudinaryService.uploadFile(
          file,
          'users-profile',
        );

        if (
          user.profile.avatar &&
          this.cloudinaryService.isCloudinaryUrl(user.profile.avatar)
        ) {
          const publicId = this.cloudinaryService.checkPublicid(
            user.profile.avatar,
          );
          if (publicId) {
            await this.cloudinaryService.deleteFile(publicId);
            console.log(`Successfully deleted old profile image: ${publicId}`);
          }
        }
        updateData.avatar = image.secure_url;
      }

      const updatedProfile = await this.profileRepository.update(
        user.profile.id,
        {
          ...updateData,
        },
      );

      return {
        ...updatedProfile,
        user,
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async updateUserProfileImage(userId: string, file: Express.Multer.File) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user) throw new NotFoundException('User not found.');

      // Only delete old image if it's a Cloudinary URL with valid public ID
      if (
        user.profile.avatar &&
        this.cloudinaryService.isCloudinaryUrl(user.profile.avatar)
      ) {
        const publicId = this.cloudinaryService.checkPublicid(
          user.profile.avatar,
        );

        console.log(publicId, 'publicId');
        if (publicId) {
          try {
            await this.cloudinaryService.deleteFile(publicId);
            console.log(`Successfully deleted old profile image: ${publicId}`);
          } catch (error) {
            console.error(
              `Failed to delete old profile image: ${publicId}`,
              error,
            );
            // Continue with the update even if deletion fails
          }
        }
      } else if (user.profile.avatar) {
        console.log(
          `Skipping deletion - not a valid Cloudinary URL: ${user.profile.avatar}`,
        );
      }
      const image = await this.cloudinaryService.uploadFile(
        file,
        'users-profile',
      );
      return await this.profileRepository.update(user.profile.id, {
        avatar: image.secure_url,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getUserProfile(userId: string) {
    try {
      const profile = await this.profileRepository.findOne(userId);
      if (!profile) throw new NotFoundException('Profile not found.');

      const orderCount = await this.ordersRepository.getUserOrderCount(userId);

      return {
        user: profile.user,
        orders: orderCount,
        profile: {
          nickname: profile.nickname,
          dateOfBirth: profile.dateOfBirth,
          otherNumbers: profile.otherNumbers,
          gender: profile.gender,
          avatar: profile.avatar,
        },
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
