import { Prisma } from '@prisma/client';

const discountSelect = {
  id: true,
  code: true,
  type: true,
  value: true,
  minOrderAmount: true,
  maxDiscount: true,
  startDate: true,
  endDate: true,
  isActive: true,
  usageLimit: true,
  usageCount: true,
  createdAt: true,
  updatedAt: true,
} as const;

export const DiscountSelect = discountSelect;
export type NewDiscount = Prisma.DiscountCreateInput;
export type Discount = Prisma.DiscountGetPayload<{
  select: typeof discountSelect;
}>;
