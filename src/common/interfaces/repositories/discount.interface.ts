import { Discount, NewDiscount } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface IDiscountRepository {
  create(data: NewDiscount): Promise<Discount>;
  findAll(params: PaginationParams): Promise<PaginatedResult<Discount[]>>;
  findOrCreteFistOrderDiscount(): Promise<Discount>;
  findOne(id: string): Promise<Discount>;
  findByCode(code: string): Promise<Discount>;
  update(id: string, data: any): Promise<Discount>;
  remove(id: string): Promise<void>;
}
